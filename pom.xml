<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.glowxq</groupId>
    <artifactId>glowxq-api</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>common</module>
        <module>app</module>
        <module>dependencies</module>
        <module>business</module>
    </modules>
    <parent>
        <groupId>com.glowxq</groupId>
        <artifactId>build</artifactId>
        <version>${revision}</version>
        <relativePath>./build/pom.xml</relativePath>
    </parent>
    <!--配置全局属性-->
    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <java.version>21</java.version>
        <skipTests>true</skipTests>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.glowxq</groupId>
                <artifactId>dependencies</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!--全局通用依赖-->
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
    </dependencies>
    <!--maven仓库加速，按需引用-->
   <!-- <repositories>
        &lt;!&ndash;阿里云仓库&ndash;&gt;
        <repository>
            <id>public</id>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        &lt;!&ndash;华为云仓库&ndash;&gt;
        <repository>
            <id>huaweicloudmaven</id>
            <url>https://repo.huaweicloud.com/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        &lt;!&ndash;腾讯云仓库&ndash;&gt;
        <repository>
            <id>tencentcloudmaven</id>
            <url>https://mirrors.tencent.com/nexus/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        &lt;!&ndash;中科大仓库&ndash;&gt;
        <repository>
            <id>ustcmaven</id>
            <url>https://maven.ustc.edu.cn/maven2/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        &lt;!&ndash; 配置官方Maven中央仓库 &ndash;&gt;
        <repository>
            <id>central</id>
            <url>https://repo1.maven.org/maven2</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>-->

    <build>
        <plugins>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>2.43.0</version>
                <configuration>
                    <java>
                        <encoding>UTF-8</encoding>
                        <eclipse>
                            <file>build/baeldung-style.xml</file>
                        </eclipse>
                        <removeUnusedImports/>
                    </java>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>