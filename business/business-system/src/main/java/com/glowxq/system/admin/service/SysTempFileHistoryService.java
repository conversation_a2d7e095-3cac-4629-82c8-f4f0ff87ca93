package com.glowxq.system.admin.service;

import com.mybatisflex.core.service.IService;
import com.glowxq.system.admin.pojo.dto.systempfile.SysTempFileHistoryCreateDTO;
import com.glowxq.system.admin.pojo.dto.systempfile.SysTempFileHistoryListDTO;
import com.glowxq.system.admin.pojo.po.SysTempFileHistory;
import com.glowxq.core.common.entity.PageResult;

/**
 * <p>
 * 模版文件历史 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
public interface SysTempFileHistoryService extends IService<SysTempFileHistory> {

    void create(SysTempFileHistoryCreateDTO dto);

    PageResult<SysTempFileHistory> historyList(SysTempFileHistoryListDTO dto);
}