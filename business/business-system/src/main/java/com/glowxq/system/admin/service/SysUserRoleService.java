package com.glowxq.system.admin.service;

import com.mybatisflex.core.service.IService;
import com.glowxq.system.admin.pojo.po.SysUserRole;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 系统用户-角色关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-01
 */
public interface SysUserRoleService extends IService<SysUserRole> {

    List<String> getUserRolesByUserId(Long userId);

    List<String>  getUserRoleKeysByUserId(Long userId);
}
