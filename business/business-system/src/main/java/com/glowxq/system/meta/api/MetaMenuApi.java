package com.glowxq.system.meta.api;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.*;
import com.glowxq.system.meta.pojo.dto.MetaMenuCreateDTO;
import com.glowxq.system.meta.pojo.dto.MetaMenuListDTO;
import com.glowxq.system.meta.pojo.dto.MetaMenuUpdateDTO;
import com.glowxq.system.meta.pojo.vo.MetaMenuVO;
import com.glowxq.system.meta.service.MetaMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * meta/菜单 Api
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Tag(name =  "菜单")
@RestController
@SaIgnore
@RequestMapping("/client")
@RequiredArgsConstructor
public class MetaMenuApi extends BaseApi  {

    private final MetaMenuService metaMenuService;


    @Operation(summary = "列表查询")
    @GetMapping("/meta-menu/list")
    public ApiResult<PageResult<MetaMenuVO>> list(MetaMenuListDTO dto) {
        return ApiPageResult.success(metaMenuService.page(dto));
    }
}