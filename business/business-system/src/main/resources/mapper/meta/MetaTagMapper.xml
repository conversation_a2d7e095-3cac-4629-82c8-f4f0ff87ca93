<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.system.meta.mapper.MetaTagMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.system.meta.pojo.po.MetaTag">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="background_color" property="backgroundColor"/>
        <result column="text_color" property="textColor"/>
        <result column="plain" property="plain"/>
        <result column="enable" property="enable"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, background_color, text_color, plain, enable, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
