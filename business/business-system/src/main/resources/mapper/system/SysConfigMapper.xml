<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.system.admin.mapper.SysConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.glowxq.system.admin.pojo.po.SysConfig">
        <id column="id" property="id"/>
        <result column="config_name" property="configName"/>
        <result column="config_key" property="configKey"/>
        <result column="config_value" property="configValue"/>
        <result column="is_lock" property="isLock"/>
        <result column="create_id" property="createId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_name, config_key, config_value, is_lock, create_id, create_time, update_id, update_time, remark
    </sql>

</mapper>
