<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.system.admin.mapper.SysRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.glowxq.system.admin.pojo.po.SysRole">
        <id column="id" property="id"/>
        <result column="role_name" property="roleName"/>
        <result column="remark" property="remark"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
        <result column="is_lock" property="isLock"/>
        <result column="permissions" property="permissions"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, role_name, remark, del_flag, create_time, update_time, create_id, update_id, is_lock, permissions
    </sql>

</mapper>
