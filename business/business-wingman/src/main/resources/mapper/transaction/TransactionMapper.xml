<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.wingman.transaction.mapper.TransactionMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.wingman.transaction.pojo.po.Transaction">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="order_number" property="orderNumber"/>
        <result column="transaction_number" property="transactionNumber"/>
        <result column="amount" property="amount"/>
        <result column="time" property="time"/>
        <result column="type" property="type"/>
        <result column="handle_user" property="handleUser"/>
        <result column="expert_user_id" property="expertUserId"/>
        <result column="expert_name" property="expertName"/>
        <result column="expert_contact" property="expertContact"/>
        <result column="expert_qr_code" property="expertQrCode"/>
        <result column="remark" property="remark"/>
        <result column="enable" property="enable"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, order_number, transaction_number, amount, time, type, handle_user, expert_user_id, expert_name, expert_contact, expert_qr_code, remark, enable, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
