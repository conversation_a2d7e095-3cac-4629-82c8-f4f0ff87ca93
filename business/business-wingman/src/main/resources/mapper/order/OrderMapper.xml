<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.wingman.order.mapper.OrderMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.wingman.order.pojo.po.Order">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="product_type" property="productType"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="order_number" property="orderNumber"/>
        <result column="order_user" property="orderUser"/>
        <result column="order_phone" property="orderPhone"/>
        <result column="name" property="name"/>
        <result column="price" property="price"/>
        <result column="payment_amount" property="paymentAmount"/>
        <result column="content" property="content"/>
        <result column="type" property="type"/>
        <result column="inventory" property="inventory"/>
        <result column="image" property="image"/>
        <result column="image_gallery" property="imageGallery"/>
        <result column="expert_user_id" property="expertUserId"/>
        <result column="expert_name" property="expertName"/>
        <result column="expert_contact" property="expertContact"/>
        <result column="expert_qr_code" property="expertQrCode"/>
        <result column="service_time" property="serviceTime"/>
        <result column="status" property="status"/>
        <result column="enable" property="enable"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, product_id, product_name, product_type, transaction_id, order_number, order_user, order_phone, name, price, payment_amount, content, type, inventory, image, image_gallery, expert_user_id, expert_name, expert_contact, expert_qr_code, service_time, status, enable, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
