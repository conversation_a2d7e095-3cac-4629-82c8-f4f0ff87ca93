package com.glowxq.wingman.notify.peymant;

import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.glowxq.wechat.pay.service.impl.BaseWechatPaymentNotifyServiceImpl;
import com.glowxq.wechat.pay.utils.PaymentUtils;
import com.glowxq.wingman.order.enums.OrderStatus;
import com.glowxq.wingman.order.pojo.po.Order;
import com.glowxq.wingman.order.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Slf4j
@Component
@Primary
@RequiredArgsConstructor
public class WingmanWechatPaymentNotifyServiceImpl extends BaseWechatPaymentNotifyServiceImpl {

    private final OrderService orderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doPaymentNotify(WxPayNotifyV3Result wxPayNotifyV3Result) {
        WxPayNotifyV3Result.DecryptNotifyResult notifyV3ResultResult = wxPayNotifyV3Result.getResult();
        log.info("支付回调结果：{}", notifyV3ResultResult);
        Order order = orderService.getByOrderNumber(notifyV3ResultResult.getOutTradeNo());
        order.setTransactionId(notifyV3ResultResult.getTransactionId());
        order.setStatus(OrderStatus.Payment.getCode());
        BigDecimal paymentAmount = PaymentUtils.formatFightsToAmount(notifyV3ResultResult.getAmount().getTotal());
        order.setPaymentAmount(paymentAmount);
        order.setPaymentTime(LocalDateTime.now());
        orderService.updateById(order);
        log.info("订单支付成功，订单号：{}", order.getOrderNumber());
    }
}
