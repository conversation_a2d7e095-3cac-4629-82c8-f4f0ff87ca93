package com.glowxq.wingman.article.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.system.meta.enums.TagBusinessBindType;
import com.glowxq.system.meta.pojo.po.MetaTag;
import com.glowxq.system.meta.service.MetaTagService;
import com.glowxq.wingman.article.mapper.ArticleMapper;
import com.glowxq.wingman.article.pojo.dto.ArticleCreateDTO;
import com.glowxq.wingman.article.pojo.dto.ArticleImportDTO;
import com.glowxq.wingman.article.pojo.dto.ArticleListDTO;
import com.glowxq.wingman.article.pojo.dto.ArticleUpdateDTO;
import com.glowxq.wingman.article.pojo.po.Article;
import com.glowxq.wingman.article.pojo.vo.ArticleVO;
import com.glowxq.wingman.article.service.ArticleService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 案例 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Service
@RequiredArgsConstructor
public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> implements ArticleService {

    private final MetaTagService metaTagService;

    private static QueryWrapper buildQueryWrapper(ArticleListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(Article.class);
        wrapper.eq(Article::getUserId, dto.getUserId(), Utils.isNotNull(dto.getUserId()));
        wrapper.eq(Article::getExpertUserId, dto.getExpertUserId(), Utils.isNotNull(dto.getExpertUserId()));
        wrapper.eq(Article::getCategoryId, dto.getCategoryId(), Utils.isNotNull(dto.getCategoryId()));
        wrapper.eq(Article::getArticleUser, dto.getArticleUser(), Utils.isNotNull(dto.getArticleUser()));
        wrapper.eq(Article::getTitle, dto.getTitle(), Utils.isNotNull(dto.getTitle()));
        wrapper.eq(Article::getPrice, dto.getPrice(), Utils.isNotNull(dto.getPrice()));
        wrapper.eq(Article::getContent, dto.getContent(), Utils.isNotNull(dto.getContent()));
        wrapper.eq(Article::getImage, dto.getImage(), Utils.isNotNull(dto.getImage()));
        wrapper.eq(Article::getImageGallery, dto.getImageGallery(), Utils.isNotNull(dto.getImageGallery()));
        wrapper.eq(Article::getExpertContact, dto.getExpertContact(), Utils.isNotNull(dto.getExpertContact()));
        wrapper.eq(Article::getExpertQrCode, dto.getExpertQrCode(), Utils.isNotNull(dto.getExpertQrCode()));
        wrapper.eq(Article::getEnable, dto.getEnable(), Utils.isNotNull(dto.getEnable()));
        return wrapper;
    }

    @Override
    public void create(ArticleCreateDTO dto) {
        Article article = BeanCopyUtils.copy(dto, Article.class);
        save(article);
    }

    @Override
    public void update(ArticleUpdateDTO dto) {
        Article article = BeanCopyUtils.copy(dto, Article.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                              .eq(Article::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(article);
    }

    @Override
    public PageResult<ArticleVO> page(ArticleListDTO dto) {
        QueryWrapper queryWrapper = buildQueryWrapper(dto);
        List<Long> articleIds = metaTagService.listBusinessIdByTagIds(dto.getTagIds(), TagBusinessBindType.Article);
        queryWrapper.in(Article::getId, articleIds, CollectionUtils.isNotEmpty(articleIds));
        Page<ArticleVO> page = pageAs(PageUtils.getPage(dto), queryWrapper, ArticleVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<ArticleVO> list(ArticleListDTO dto) {
        return listAs(buildQueryWrapper(dto), ArticleVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public ArticleVO detail(Object id) {
        Article article = getById((Serializable) id);
        CommonResponseEnum.INVALID_ID.assertNull(article);
        List<MetaTag> metaTags = metaTagService.listByBusinessId(article.getId(), TagBusinessBindType.Article);
        ArticleVO articleVO = BeanCopyUtils.copy(article, ArticleVO.class);
        articleVO.setTagList(metaTags);
        return articleVO;
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<ArticleImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), ArticleImportDTO.class, true);
        List<ArticleImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(ArticleListDTO dto, HttpServletResponse response) {
        List<ArticleVO> list = list(dto);
        String fileName = "案例模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "案例", ArticleVO.class, os);
    }
}