package com.glowxq.wingman.transaction.service.impl;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.PageUtils;
import com.glowxq.core.util.Utils;
import com.glowxq.excel.core.ExcelResult;
import com.glowxq.excel.utils.ExcelUtils;
import com.glowxq.wingman.order.pojo.po.Order;
import com.glowxq.wingman.order.service.OrderService;
import com.glowxq.wingman.transaction.mapper.TransactionMapper;
import com.glowxq.wingman.transaction.pojo.dto.TransactionCreateDTO;
import com.glowxq.wingman.transaction.pojo.dto.TransactionImportDTO;
import com.glowxq.wingman.transaction.pojo.dto.TransactionListDTO;
import com.glowxq.wingman.transaction.pojo.dto.TransactionUpdateDTO;
import com.glowxq.wingman.transaction.pojo.po.Transaction;
import com.glowxq.wingman.transaction.pojo.po.TransactionSummary;
import com.glowxq.wingman.transaction.pojo.vo.TransactionVO;
import com.glowxq.wingman.transaction.service.TransactionService;
import com.glowxq.wingman.transaction.service.TransactionSummaryService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.OutputStream;
import java.io.Serializable;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 流水 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Service
@RequiredArgsConstructor
public class TransactionServiceImpl extends ServiceImpl<TransactionMapper, Transaction> implements TransactionService {

    private final TransactionSummaryService transactionSummaryService;

    private final OrderService orderService;

    private static QueryWrapper buildQueryWrapper(TransactionListDTO dto) {
        QueryWrapper wrapper = QueryWrapper.create().from(Transaction.class);
        wrapper.eq(Transaction::getOrderId, dto.getOrderId(), Utils.isNotNull(dto.getOrderId()));
        wrapper.eq(Transaction::getOrderNumber, dto.getOrderNumber(), Utils.isNotNull(dto.getOrderNumber()));
        wrapper.eq(Transaction::getTransactionNumber, dto.getTransactionNumber(), Utils.isNotNull(dto.getTransactionNumber()));
        wrapper.eq(Transaction::getAmount, dto.getAmount(), Utils.isNotNull(dto.getAmount()));
        wrapper.between(Transaction::getTime,
                dto.getTimeStart(),
                dto.getTimeEnd(),
                Utils.isNotNull(dto.getTimeStart()) && Utils.isNotNull(dto.getTimeEnd()));
        wrapper.eq(Transaction::getType, dto.getType(), Utils.isNotNull(dto.getType()));
        wrapper.eq(Transaction::getHandleUser, dto.getHandleUser(), Utils.isNotNull(dto.getHandleUser()));
        wrapper.eq(Transaction::getExpertUserId, dto.getExpertUserId(), Utils.isNotNull(dto.getExpertUserId()));
        wrapper.like(Transaction::getExpertName, dto.getExpertName(), Utils.isNotNull(dto.getExpertName()));
        wrapper.eq(Transaction::getExpertContact, dto.getExpertContact(), Utils.isNotNull(dto.getExpertContact()));
        wrapper.eq(Transaction::getExpertQrCode, dto.getExpertQrCode(), Utils.isNotNull(dto.getExpertQrCode()));
        wrapper.eq(Transaction::getEnable, dto.getEnable(), Utils.isNotNull(dto.getEnable()));
        return wrapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(TransactionCreateDTO dto) {
        Transaction transaction = BeanCopyUtils.copy(dto, Transaction.class);
        save(transaction);

        Order order = orderService.getByOrderNumber(transaction.getOrderNumber());
        CommonResponseEnum.INVALID_ID.assertNull(order);
        TransactionSummary transactionSummary = Optional.ofNullable(transactionSummaryService.getByOrderNumber(transaction.getOrderNumber()))
                                                        .orElse(new TransactionSummary());

        transactionSummary.setOrderId(order.getId());
        transactionSummary.setOrderNumber(order.getOrderNumber());
        transactionSummary.setTransactionNumber("");
        transactionSummary.setOrderAmount(order.getPaymentAmount());
        transactionSummary.calculateTransaction(transaction);
        transactionSummaryService.saveOrUpdate(transactionSummary);
    }

    @Override
    public void update(TransactionUpdateDTO dto) {
        Transaction transaction = BeanCopyUtils.copy(dto, Transaction.class);
        QueryWrapper wrapper;
        // id有效性校验
        wrapper = QueryWrapper.create()
                              .eq(Transaction::getId, dto.getId());
        CommonResponseEnum.INVALID_ID.assertTrue(count(wrapper) <= 0);

        saveOrUpdate(transaction);
    }

    @Override
    public PageResult<TransactionVO> page(TransactionListDTO dto) {
        Page<TransactionVO> page = pageAs(PageUtils.getPage(dto), buildQueryWrapper(dto), TransactionVO.class);
        return PageUtils.getPageResult(page);
    }

    @Override
    public List<TransactionVO> list(TransactionListDTO dto) {
        return listAs(buildQueryWrapper(dto), TransactionVO.class);
    }

    @Override
    public void remove(SelectIdsDTO dto) {
        CommonResponseEnum.INVALID_ID.assertTrue(dto.getIds().isEmpty());
        removeByIds(dto.getIds());
    }

    @Override
    public TransactionVO detail(Long id) {
        Transaction transaction = getById(id);
        CommonResponseEnum.INVALID_ID.assertNull(transaction);
        return BeanCopyUtils.copy(transaction, TransactionVO.class);
    }

    @SneakyThrows
    @Override
    public void importExcel(ImportExcelDTO dto) {
        ExcelResult<TransactionImportDTO> excelResult = ExcelUtils.importExcel(dto.getFile().getInputStream(), TransactionImportDTO.class, true);
        List<TransactionImportDTO> list = excelResult.getList();
        List<String> errorList = excelResult.getErrorList();
        String analysis = excelResult.getAnalysis();
        System.out.println(" analysis : " + analysis);
        System.out.println(" isCover : " + dto.getIsCover());
    }

    @SneakyThrows
    @Override
    public void exportExcel(TransactionListDTO dto, HttpServletResponse response) {
        List<TransactionVO> list = list(dto);
        String fileName = "流水模板";
        OutputStream os = FileUtils.getOutputStream(response, fileName + ".xlsx");
        ExcelUtils.exportExcel(list, "流水", TransactionVO.class, os);
    }
}