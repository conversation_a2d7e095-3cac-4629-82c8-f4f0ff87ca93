<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.oj.group.mapper.GroupBindMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.oj.group.pojo.po.GroupBind">
        <id column="id" property="id"/>
        <result column="group_id" property="groupId"/>
        <result column="business_id" property="businessId"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, group_id, business_id, type, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
