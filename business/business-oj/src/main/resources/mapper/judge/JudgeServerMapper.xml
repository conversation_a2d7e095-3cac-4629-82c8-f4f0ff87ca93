<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glowxq.oj.judge.mapper.JudgeServerMapper">
    <!-- 通用映射 -->
    <resultMap id="BaseResultMap" type="com.glowxq.oj.judge.pojo.po.JudgeServer">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="ip" property="ip"/>
        <result column="port" property="port"/>
        <result column="url" property="url"/>
        <result column="cpu_core" property="cpuCore"/>
        <result column="free_memory" property="freeMemory"/>
        <result column="task_number" property="taskNumber"/>
        <result column="max_task_number" property="maxTaskNumber"/>
        <result column="enable" property="enable"/>
        <result column="remote_enable" property="remoteEnable"/>
        <result column="cf_submittable_enable" property="cfSubmittableEnable"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, ip, port, url, cpu_core, free_memory, task_number, max_task_number, enable, remote_enable, cf_submittable_enable, create_time, update_time, del_flag, create_id, update_id
    </sql>

</mapper>
