package com.glowxq.oj.problem.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import com.glowxq.core.common.entity.ApiPageResult;
import com.glowxq.core.common.entity.ApiResult;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.oj.problem.service.ProblemCodeTemplateService;
import com.glowxq.oj.problem.pojo.dto.ProblemCodeTemplateCreateDTO;
import com.glowxq.oj.problem.pojo.dto.ProblemCodeTemplateUpdateDTO;
import com.glowxq.oj.problem.pojo.dto.ProblemCodeTemplateListDTO;
import com.glowxq.oj.problem.pojo.vo.ProblemCodeTemplateVO;
import com.glowxq.core.common.entity.ImportExcelDTO;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <p>
 * 题目代码模版 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Tag(name =  "题目代码模版")
@RestController
@RequestMapping("problem-code-template")
@RequiredArgsConstructor
public class ProblemCodeTemplateController  {

    private final ProblemCodeTemplateService problemCodeTemplateService;

    @Operation(summary = "新增")
    @SaCheckPermission(value = "problem.code.template.create")
    @PostMapping
    public ApiResult<Void> create(@RequestBody ProblemCodeTemplateCreateDTO dto) {
        problemCodeTemplateService.create(dto);
        return ApiResult.success();
    }

    @Operation(summary = "修改")
    @SaCheckPermission(value = "problem.code.template.update")
    @PutMapping
    public ApiResult<Void> update(@RequestBody ProblemCodeTemplateUpdateDTO dto) {
        problemCodeTemplateService.update(dto);
        return ApiResult.success();
    }

    @Operation(summary = "删除")
    @SaCheckPermission(value = "problem.code.template.remove")
    @DeleteMapping
    public ApiResult<Void> remove(@RequestBody SelectIdsDTO dto) {
        problemCodeTemplateService.remove(dto);
        return ApiResult.success();
    }

    @Operation(summary = "列表查询")
    @SaCheckPermission(value = "problem.code.template.query_table")
    @GetMapping
    public ApiResult<PageResult<ProblemCodeTemplateVO>> list(ProblemCodeTemplateListDTO dto) {
        return ApiPageResult.success(problemCodeTemplateService.page(dto));
    }

    @Operation(summary = "详情")
    @SaCheckPermission(value = "problem.code.template.query_table")
    @GetMapping("/{id}")
    public ApiResult<ProblemCodeTemplateVO> detail(@PathVariable Object id) {
        return ApiResult.success(problemCodeTemplateService.detail(id));
    }

    @Operation(summary = "导入")
    @Parameters({
      @Parameter(name = "file", description = "上传文件", schema = @Schema(type = "string", format = "binary"), required = true),
    })
    @SaCheckPermission(value = "problem.code.template.import")
    @PostMapping("/import")
    public void importExcel(@ModelAttribute ImportExcelDTO dto) {
        problemCodeTemplateService.importExcel(dto);
    }

    @Operation(summary = "导出")
    @SaCheckPermission(value = "problem.code.template.export")
    @PostMapping("/export")
    public void exportExcel(@RequestBody ProblemCodeTemplateListDTO dto, HttpServletResponse response) {
        problemCodeTemplateService.exportExcel(dto, response);
    }
}