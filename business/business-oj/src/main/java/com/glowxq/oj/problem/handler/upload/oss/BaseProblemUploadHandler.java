package com.glowxq.oj.problem.handler.upload.oss;

import com.glowxq.core.common.exception.common.AlertsException;
import com.glowxq.core.common.exception.common.BusinessException;
import com.glowxq.core.util.bo.DownloadResult;
import com.glowxq.core.util.FileUtils;
import com.glowxq.core.util.http.HttpUtils;
import com.glowxq.oj.problem.enums.FilePath;
import com.glowxq.oj.problem.pojo.bo.ProblemImportProgramBO;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 题目上传处理器基类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/28
 */
@Slf4j
public abstract class BaseProblemUploadHandler implements ProblemUploadInterface {

    private final FilePath tempFilePath = FilePath.PROBLEM_UPLOAD_TMP_FOLDER;

    @Override
    public List<String> upload(ProblemImportProgramBO problemImportProgramBO) {
        log.info("开始从导入编程题题目:{}", problemImportProgramBO);
        File uploadZipFile = null;
        boolean isLocalFile = isLocalFile(problemImportProgramBO.getFileUrl());

        try {
            if (isLocalFile) {
                // 如果是本地文件，直接使用该文件
                uploadZipFile = new File(problemImportProgramBO.getFileUrl().replace("file://", ""));
                log.info("使用本地文件: {}", uploadZipFile.getAbsolutePath());
            } else {
                // 创建临时文件
                uploadZipFile = createTempFile(problemImportProgramBO.getFileUrl());
                log.info("成功创建临时文件: {}", uploadZipFile.getAbsolutePath());

                // 下载文件到临时文件
                downloadFile(problemImportProgramBO.getFileUrl(), uploadZipFile);
                log.info("成功从 url 下载文件 {} 到本地 {}", problemImportProgramBO.getFileUrl(), uploadZipFile.getAbsolutePath());
            }

            unZipFile(uploadZipFile, problemImportProgramBO);

            // 校验文件是否符合要求
            validateFile(uploadZipFile, problemImportProgramBO);
            log.info("文件校验通过: {}", uploadZipFile.getName());

            // 处理上传逻辑（由子类实现具体逻辑）
            List<String> processedUpload = processUpload(uploadZipFile, problemImportProgramBO);
            log.info("成功从url导入题目，数量：{}", processedUpload.size());
            return processedUpload;
        } finally {
            // 确保删除临时文件（只删除非本地文件）
            if (uploadZipFile != null && !isLocalFile) {
                deleteTempFile(uploadZipFile);
                log.info("已删除临时文件: {}", uploadZipFile.getAbsolutePath());
            }
        }
    }

    protected void unZipFile(File tempFile, ProblemImportProgramBO problemImportProgramBo) {
        File unzipDir = FileUtils.createTargetDirectoryBySourceFile(tempFile);
        try {
            FileUtils.unzip(tempFile, unzipDir);
        } catch (IOException e) {
            log.error(" 文件解压失败: {}", tempFile.getName(), e);
            FileUtils.del(tempFile);
            FileUtils.del(unzipDir);
            throw new AlertsException(tempFile.getName()+"解压失败");
        }
        problemImportProgramBo.setUnZipDir(unzipDir);
    }

    /**
     * 判断是否为本地文件路径
     *
     * @param fileUrl 文件路径或URL
     * @return true表示本地文件，false表示远程URL
     */
    protected boolean isLocalFile(String fileUrl) {
        return fileUrl != null && fileUrl.startsWith("file://");
    }

    /**
     * 创建临时文件（保持与OSS文件相同的后缀格式）
     *
     * @param url OSS文件地址
     * @return 创建的临时文件对象
     * @throws AlertsException 当创建临时文件失败时抛出
     */
    protected File createTempFile(String url) {
        // 获取文件后缀（从最后一个.开始截取）
        String fileExtension = "";
        int lastDotIndex = url.lastIndexOf('.');
        if (lastDotIndex > 0) {
            fileExtension = url.substring(lastDotIndex);
        }

        // 构建临时文件路径 - 直接使用FilePath.buildPath() 方法生成完整路径
        String tempFileName = tempFilePath.buildPath(UUID.randomUUID() + fileExtension);
        File tempFile = new File(tempFileName);

        // 确保父目录存在
        File parentDir = tempFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (!created) {
                log.error(" 创建临时目录失败: {}", parentDir.getAbsolutePath());
                throw new AlertsException("无法创建临时目录：" + parentDir.getAbsolutePath());
            }
        }

        return tempFile;
    }

    /**
     * 从OSS下载文件到本地
     *
     * @param ossUrl     OSS文件地址
     * @param targetFile 目标存储文件
     * @throws AlertsException 当下载失败时抛出
     */
    protected void downloadFile(String ossUrl, File targetFile) {
        try {
            // 使用HttpUtils下载文件
            DownloadResult result = HttpUtils.download(ossUrl);

            // 将下载的文件内容写入目标文件
            try (FileOutputStream fos = new FileOutputStream(targetFile)) {
                fos.write(result.getData());
                fos.flush();
            }

            log.debug(" 成功从OSS下载文件 {} 到本地 {}", ossUrl, targetFile.getAbsolutePath());
        } catch (Exception e) {
            log.error(" 从OSS下载文件失败: {}", ossUrl, e);
            throw new AlertsException("OSS文件下载失败：" + e.getMessage());
        }
    }

    /**
     * 校验文件是否符合要求,并解压文件
     *
     * @param tempFile 待校验的临时文件
     * @param dto      导入数据传输对象
     * @throws BusinessException 当文件不符合要求时抛出
     */
    protected abstract void validateFile(File tempFile, ProblemImportProgramBO dto) throws BusinessException;

    /**
     * 处理上传逻辑
     *
     * @param tempFile 下载的临时文件
     * @param problemImportProgramBO      导入数据传输对象
     * @return
     */
    protected abstract List<String> processUpload(File tempFile, ProblemImportProgramBO problemImportProgramBO);

    /**
     * 删除临时文件
     *
     * @param tempFile 要删除的临时文件
     * @throws AlertsException 当删除文件失败时抛出
     */
    protected void deleteTempFile(File tempFile) {
        if (tempFile != null && tempFile.exists()) {
            try {
                boolean deleted = tempFile.delete();
                if (deleted) {
                    log.debug(" 已删除临时文件: {}", tempFile.getAbsolutePath());
                } else {
                    log.warn(" 删除临时文件失败: {}", tempFile.getAbsolutePath());
                    throw new AlertsException("删除临时文件失败：" + tempFile.getAbsolutePath());
                }
            } catch (SecurityException e) {
                log.error(" 删除临时文件时发生安全异常: {}", tempFile.getAbsolutePath(), e);
                throw new AlertsException("删除临时文件" + tempFile.getAbsolutePath() + "时发生安全异常：" + e.getMessage());
            }
        }
    }

    /**
     * 校验指定文件夹中的输入文件（.in）和输出文件（.out）是否符合要求。
     * <p>
     * 该方法的主要功能是：
     * <ol>
     *   <li>获取指定文件夹中所有以 {@code inFileExt} 和 {@code outFileExt} 为扩展名的文件。</li>
     *   <li>检查输入文件和输出文件的数量是否相等。</li>
     *   <li>确保每个输入文件都有一个对应的输出文件（通过文件名匹配）。</li>
     * </ol>
     * 如果校验失败，则会抛出 {@link BusinessException} 异常。
     *
     * @param folder     要校验的文件夹路径。必须是一个有效的目录，不能为空。
     * @param inFileExt  输入文件的扩展名（如 "in"）。必须省略开头的点号，不能为空。
     * @param outFileExt 输出文件的扩展名（如 "out"）。必须省略开头的点号，不能为空。
     * @param folderName 文件夹的名称或标识符，用于在异常消息中提供上下文信息。不能为空。
     * @throws BusinessException 如果以下任意一种情况发生：
     *                           <ul>
     *                             <li>输入文件和输出文件的数量不匹配。</li>
     *                             <li>某个输入文件缺少对应的输出文件。</li>
     *                           </ul>
     */
    protected void validateInAndOutFile(File folder, String inFileExt, String outFileExt, String folderName) {
        // 2. 获取所有 .in 和 .out 文件
        List<File> inFileList = FileUtils.listFilesByExtension(folder, inFileExt);
        List<File> outFileList = FileUtils.listFilesByExtension(folder, outFileExt);

        // 检查 .in 和 .out 文件数量是否相等
        if (inFileList.size() != outFileList.size()) {
            throw new BusinessException("文件夹 " + folderName + " 中的 ." + inFileExt + " 文件和 ." + outFileExt + " 文件数量不匹配");
        }

        // 3. 使用 Set 存储 .out 文件名，便于快速查找
        Set<String> outFilenames = outFileList.stream()
                .map(File::getName)
                .collect(Collectors.toSet());

        // 遍历 .in 文件并检查是否有对应的 .out 文件
        for (File inFile : inFileList) {
            String outFileName = inFile.getName().replace("." + inFileExt, "." + outFileExt);
            if (!outFilenames.contains(outFileName)) {
                throw new BusinessException("文件夹 " + folderName + " 中的 ." + inFileExt + " 文件 " + inFile.getName() + " 缺少对应的 ." + outFileExt + " 文件");
            }
        }
    }
}