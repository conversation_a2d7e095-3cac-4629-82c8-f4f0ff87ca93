package com.glowxq.oj.system.bo;

import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.oj.user.pojo.po.UserInfo;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/2
 */
@Data
public class OjUserInfo {

    // 系统主表信息

    @Id(keyType = KeyType.Auto)
    @Schema(description = "id")
    private Long id;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "密码")
    private String pwd;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "真实姓名")
    private String name;

    @Schema(description = "性别(0 未知 1 男 2 女)")
    private Integer sex;

    @Schema(description = "生日")
    private String birthday;

    @Schema(description = "头像地址")
    private String logo;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "身份证")
    private String idCard;

    @Schema(description = "邮箱地址")
    private String email;

    @Schema(description = "账户状态 (如 冻结；禁言；正常。 关联字典表account_status)")
    private String accountStatusCd;

    @Schema(description = "标签（自定义关联到字典表）")
    private String userTagCd;

    @Schema(description = "最近一次登录时间")
    private LocalDateTime lastLoginTime;

    // OJ 拓展用户信息
    @Schema(description = "ac数量")
    private Integer acNum;

    @Schema(description = "用户积分")
    private Integer integral;

    @Schema(description = "连续签到时间")
    private Integer continuousSignDay;

    @Schema(description = "提交题目数量")
    private Integer submitNum;

    @Schema(description = "称号")
    private String title;

    @Schema(description = "颜色")
    private String color;

    // 公共字段

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "租户ID")
    private String tenantId;

    @Column(isLogicDelete = true)
    @Schema(description = "是否删除")
    private String delFlag;

    @Schema(description = "创建人")
    private Long createId;

    @Schema(description = "更新人")
    private Long updateId;

    public UserInfo toUserInfo() {
        return BeanCopyUtils.copy(this, UserInfo.class);
    }
}
