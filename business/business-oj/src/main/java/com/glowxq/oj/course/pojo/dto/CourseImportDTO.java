package com.glowxq.oj.course.pojo.dto;

import com.glowxq.oj.course.pojo.po.Course;
import com.glowxq.core.common.entity.base.BaseDTO;
import com.glowxq.core.util.BeanCopyUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

import cn.idev.excel.annotation.ExcelProperty;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * <p>
 * Course导入DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-30
 */
@Data
@Schema(description = "Course导入DTO")
public class CourseImportDTO implements BaseDTO {

    @ExcelProperty(value = "课程名")
    @Schema(description =  "课程名")
    private String name;

    @ExcelProperty(value = "课程内容")
    @Schema(description =  "课程内容")
    private String content;

    @ExcelProperty(value = "课程连接")
    @Schema(description =  "课程连接")
    private String url;

    @ExcelProperty(value = "老师用户id")
    @Schema(description =  "老师用户id")
    private Long teacherUserId;

    @ExcelProperty(value = "老师姓名")
    @Schema(description =  "老师姓名")
    private String teacherName;

    @ExcelProperty(value = "老师电话")
    @Schema(description =  "老师电话")
    private String teacherPhone;

    @ExcelProperty(value = "课程状态")
    @Schema(description =  "课程状态")
    private String status;

    @ExcelProperty(value = "启用状态")
    @Schema(description =  "启用状态")
    private Boolean enable;

    @Schema(description =  "上课时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description =  "下课时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Override
    public Course buildEntity() {
        return BeanCopyUtils.copy(this, Course.class);
    }
}