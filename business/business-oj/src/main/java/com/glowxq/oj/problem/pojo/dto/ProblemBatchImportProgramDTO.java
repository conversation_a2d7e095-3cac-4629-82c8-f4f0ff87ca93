package com.glowxq.oj.problem.pojo.dto;

import com.glowxq.core.common.valid.annotation.InEnum;
import com.glowxq.oj.problem.enums.ProblemSourceType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "Problem批量上传DTO")
public class ProblemBatchImportProgramDTO {

    /**
     * 批量压缩包URL - 包含多个题目压缩包的压缩包
     */
    @NotBlank(message = "批量压缩包URL不能为空")
    private String batchFileUrl;

    /**
     * 题目来源
     */
    @InEnum(enumClass = ProblemSourceType.class)
    @NotNull(message = "题目来源必填")
    private ProblemSourceType problemSourceType;

    /**
     * 是否需要插入不存在的标签
     */
    @NotNull(message = "是否需要插入不存在的标签必填")
    private Boolean needInsertUnExistTag;
}
