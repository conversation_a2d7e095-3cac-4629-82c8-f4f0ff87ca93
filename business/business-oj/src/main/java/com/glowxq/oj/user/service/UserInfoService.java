package com.glowxq.oj.user.service;

import com.glowxq.core.common.entity.ImportExcelDTO;
import com.glowxq.core.common.entity.PageResult;
import com.glowxq.core.common.entity.SelectIdsDTO;
import com.glowxq.oj.user.pojo.dto.RankingDTO;
import com.glowxq.oj.user.pojo.dto.UserInfoCreateDTO;
import com.glowxq.oj.user.pojo.dto.UserInfoListDTO;
import com.glowxq.oj.user.pojo.dto.UserInfoUpdateDTO;
import com.glowxq.oj.user.pojo.po.UserInfo;
import com.glowxq.oj.user.pojo.vo.UserInfoVO;
import com.mybatisflex.core.service.IService;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 用户OJ信息 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-03
 */
public interface UserInfoService extends IService<UserInfo> {

    void create(UserInfoCreateDTO dto);

    void update(UserInfoUpdateDTO dto);

    PageResult<UserInfoVO> page(UserInfoListDTO dto);

    List<UserInfoVO> list(UserInfoListDTO dto);

    void remove(SelectIdsDTO dto);

    UserInfoVO detail(Object id);

    void importExcel(ImportExcelDTO dto);

    void exportExcel(UserInfoListDTO dto, HttpServletResponse response);

    UserInfo autoByUserId(Long userId, String tenantId);

    List<UserInfo> autoListByUserIds(List<Long> userIds);

    List<UserInfo> acRankingList(RankingDTO rankingDTO);

    List<UserInfo> acRankingList();
}