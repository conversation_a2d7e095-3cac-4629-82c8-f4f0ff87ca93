package com.glowxq.mysql;

import cn.dev33.satoken.stp.StpUtil;
import com.glowxq.core.common.entity.LoginUser;
import com.glowxq.mysql.constants.DbConstants;
import com.glowxq.security.core.util.LoginUtils;
import com.glowxq.tenant.constants.TenantConstants;
import com.glowxq.tenant.utils.TenantUtils;
import com.mybatisflex.annotation.InsertListener;
import com.mybatisflex.annotation.SetListener;
import com.mybatisflex.annotation.UpdateListener;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据表变更事件处理器。
 * <p>
 * 约定：数据表必须包含以下字段：
 * <ul>
 * <li>`create_id` - int 类型，创建者 ID</li>
 * <li>`create_time` - datetime 类型，创建时间</li>
 * <li>`update_id` - int 类型，更新者 ID</li>
 * <li>`update_time` - datetime 类型，更新时间</li>
 * </ul>
 *
 * @version 1.0
 * @since 2023-12-08
 */
@Slf4j
public class EntityChangeListener implements InsertListener, UpdateListener, SetListener {

    /**
     * 实体插入事件处理
     * <p>
     * 在实体被插入数据库前自动填充以下字段：
     * 1. 创建时间和更新时间设置为当前时间
     * 2. 填充租户ID
     * 3. 如果用户已登录，则填充创建者ID和更新者ID
     * 4. 如果用户有部门信息，则填充部门范围
     * </p>
     *
     * @param o 待插入的实体对象
     */
    @Override
    public void onInsert(Object o) {
        // 设置时间字段
        setPropertyIfPresent(o, DbConstants.FIELD_CREATE_TIME, LocalDateTime.now());
        setPropertyIfPresent(o, DbConstants.FIELD_UPDATE_TIME, LocalDateTime.now());

        // 设置租户ID
        setPropertyIfPresent(o, TenantConstants.TENANT_ID_FILED, TenantUtils.getTenantId());

        // 如果未登录，则不设置用户相关字段
        if (LoginUtils.isNotLogin()) {
            return;
        }

        // 设置创建者和更新者ID
        setPropertyIfPresent(o, DbConstants.FIELD_CREATE_ID, StpUtil.getLoginIdAsLong());
        setPropertyIfPresent(o, DbConstants.FIELD_UPDATE_ID, StpUtil.getLoginIdAsLong());

        // 设置部门范围
        LoginUser loginUser = LoginUtils.getLoginUser();
        List<Long> deptOptions = loginUser.getDepts();
        if (deptOptions.isEmpty()) {
            return;
        }
        setPropertyIfPresent(o, DbConstants.FIELD_DEPT_SCOPE, deptOptions);
    }

    /**
     * 实体更新事件处理
     * <p>
     * 在实体被更新前自动填充以下字段：
     * 1. 更新时间设置为当前时间
     * 2. 如果用户已登录，则填充更新者ID
     * </p>
     *
     * @param o 待更新的实体对象
     */
    @Override
    public void onUpdate(Object o) {
        // 设置更新时间
        setPropertyIfPresent(o, DbConstants.FIELD_UPDATE_TIME, LocalDateTime.now());

        // 如果未登录，则不设置更新者ID
        if (LoginUtils.isNotLogin()) {
            return;
        }

        // 设置更新者ID
        setPropertyIfPresent(o, DbConstants.FIELD_UPDATE_ID, StpUtil.getLoginIdAsLong());
    }

    /**
     * 属性设置事件处理
     * <p>
     * 在实体属性被设置时调用，当前实现直接返回原始值，不做处理
     * </p>
     *
     * @param entity 实体对象
     * @param property 属性名
     * @param value 属性值
     * @return 处理后的属性值
     */
    @Override
    public Object onSet(Object entity, String property, Object value) {
        return value;
    }

    /**
     * 设置实体属性值（如果属性存在）
     * <p>
     * 通过反射机制调用实体的setter方法设置属性值
     * 如果属性不存在或setter方法不可访问，则记录警告日志并忽略
     * </p>
     *
     * @param object 目标实体对象
     * @param propertyName 属性名称
     * @param propertyValue 要设置的属性值
     */
    private void setPropertyIfPresent(Object object, String propertyName, Object propertyValue) {
        try {
            // 获取对象的 Class 对象
            Class<?> clazz = object.getClass();

            // 获取属性名称对应的 getter 方法名称
            String getterName = "get" + Character.toUpperCase(propertyName.charAt(0)) + propertyName.substring(1);
            // 获取 getter 方法
            Method getterMethod = clazz.getMethod(getterName);

            Object value = getterMethod.invoke(object);
            if (value != null) {
                return;
            }

            // 获取属性名称对应的 setter 方法名称
            String setterName = "set" + Character.toUpperCase(propertyName.charAt(0)) + propertyName.substring(1);
            // 获取 setter 方法
            Method setterMethod = clazz.getMethod(setterName, propertyValue.getClass());
            // 调用 setter 方法设置字段值
            setterMethod.invoke(object, propertyValue);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            // 如果字段不存在或者 setter 方法不可访问，则忽略异常
            log.error("Fill EntityChangeField failed; Property {} not found or inaccessible. ", propertyName, e);
        }
    }
}
