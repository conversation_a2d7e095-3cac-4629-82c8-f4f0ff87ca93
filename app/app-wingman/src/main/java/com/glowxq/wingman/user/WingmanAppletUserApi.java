package com.glowxq.wingman.user;

import cn.dev33.satoken.annotation.SaIgnore;
import com.glowxq.core.common.api.BaseApi;
import com.glowxq.core.common.entity.ApiResult;
import com.glowxq.core.common.enums.CommonResponseEnum;
import com.glowxq.security.pojo.ClientVO;
import com.glowxq.security.pojo.LoginInfo;
import com.glowxq.security.pojo.LoginVO;
import com.glowxq.system.admin.service.SysClientService;
import com.glowxq.wingman.auth.WingmanPasswordStrategy;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * applet/小程序用户 Api
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/25
 */
@Tag(name = "用户接口")
@RestController
@RequestMapping("/client/applet")
public class WingmanAppletUserApi extends BaseApi {

    @Autowired
    private WingmanPasswordStrategy wingsPasswordStrategy;

    @Autowired
    private SysClientService sysClientService;

    /**
     * 账号密码登陆接口
     */
    @SaIgnore
    @PostMapping("/passwordLogin")
    public ApiResult<LoginVO> passwordLogin(@RequestBody LoginInfo info) {
        ClientVO client = sysClientService.detail(info.getClientId());
        CommonResponseEnum.CLIENT_INVALID.assertNull(client);
        LoginVO login = wingsPasswordStrategy.login(info, client);
        return ApiResult.success(login);
    }
}
