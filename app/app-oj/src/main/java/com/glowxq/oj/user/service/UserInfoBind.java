package com.glowxq.oj.user.service;

import com.glowxq.system.admin.pojo.OJUserInfoBO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/28
 */
public interface UserInfoBind {

    void bindTags(Long userId, List<Long> tagIds);

    void bindGroups(Long userId, List<Long> groupIds);

    void updateTags(Long userId, List<Long> tagIds);

    void updateGroups(Long userId, List<Long> groupIds);

    List<Long> listUserIdByTag(List<Long> tagIds);

    List<Long> listUserIdByGroup(List<Long> groupIds);

    Set<Long> listGroupIdByUserId(Long id);

    void bindUserInfo(Long id, OJUserInfoBO userInfoBo);

    OJUserInfoBO getByOjUserId(Long userId);
}
