package com.glowxq.oj.user.service.impl;

import com.glowxq.core.util.BeanCopyUtils;
import com.glowxq.core.util.StreamUtils;
import com.glowxq.oj.group.service.GroupService;
import com.glowxq.oj.system.OjUserInfoService;
import com.glowxq.oj.system.bo.OjUserInfo;
import com.glowxq.oj.user.pojo.po.UserInfo;
import com.glowxq.oj.user.service.UserInfoService;
import com.glowxq.system.admin.pojo.po.SysUser;
import com.glowxq.system.admin.service.SysUserService;
import com.glowxq.system.meta.enums.TagBusinessBindType;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/2
 */
@Component
@RequiredArgsConstructor
@Primary
public class OjUserInfoServiceImpl implements OjUserInfoService {

    private final SysUserService sysUserService;

    private final UserInfoService userInfoService;

    private final GroupService groupService;

    @Override
    public List<OjUserInfo> listByGroupId(Long groupId) {
        List<Long> userIds = groupService.listBusinessIdByGroupId(groupId, TagBusinessBindType.User);
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        // oj拓展信息
        List<UserInfo> userInfos = userInfoService.autoListByUserIds(userIds);
        Map<Long, UserInfo> userInfoMap = StreamUtils.toMap(userInfos, UserInfo::getUserId, Function.identity());

        // 用户主表信息
        List<SysUser> sysUsers = sysUserService.listByIds(userIds);
        List<OjUserInfo> ojUserInfos = BeanCopyUtils.copyList(sysUsers, OjUserInfo.class);

        // 组装信息
        List<OjUserInfo> ojUserInfoList = ojUserInfos.stream().peek(ojUserInfo -> {
            UserInfo userInfo = userInfoMap.get(ojUserInfo.getId());
            if (userInfo != null) {
                BeanCopyUtils.copy(userInfo, ojUserInfo);
            }
        }).collect(Collectors.toList());

        return ojUserInfoList;
    }

    @Override
    public OjUserInfo getByUserId(Long userId) {
        OjUserInfo ojUserInfo = new OjUserInfo();
        UserInfo userInfo = userInfoService.autoByUserId(userId, null);
        SysUser sysUser = sysUserService.getById(userId);
        BeanCopyUtils.copy(userInfo, ojUserInfo);
        BeanCopyUtils.copy(sysUser, ojUserInfo);
        return ojUserInfo;
    }
}
