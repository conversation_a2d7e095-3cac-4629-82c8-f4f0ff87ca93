package com.glowxq.oj.user.po;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * UserDeptDTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/4/2 9:44
 */
@Data
public class UseGroupBindDTO {

    @Schema(description = "用户id数组")
    @NotNull
    private List<Long> userIds = new ArrayList<>();

    @Schema(description = "班级id数组")
    @NotNull
    private List<Long> groupIds = new ArrayList<>();
}
