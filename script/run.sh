#!/bin/bash
set -e  # 基础错误检查

# 镜像配置
REG="registry.cn-guangzhou.aliyuncs.com"
REPO="glowxq/nexus"
TAG="api-oj"

# 容器配置
CONTAINER="api-oj"
RESTART="--restart=always"

# 存储配置（带默认值）
DATA_DIR="${OJ_DATA_DIRECTORY:-/mnt/data/my/oj/data}"
VOLUMES=(
  -v "$DATA_DIR/common/testcase:/goj/testcase"
  -v "$DATA_DIR/common/testcase:/judge/testcase"
  -v "$DATA_DIR/goj/file:/goj/file"
  -v "$DATA_DIR/goj/log:/goj/log"
  -v "$DATA_DIR/judge/log:/judge/log"
  -v "$DATA_DIR/judge/run:/judge/run"
  -v "$DATA_DIR/judge/spj:/judge/spj"
  -v "$DATA_DIR/judge/interactive:/judge/interactive"
)

# 运行时配置
ENV=(
  -e TZ=Asia/Shanghai
  -e "JAVA_OPTS=-Xms1024m -Xmx2048m"  # 引号保证参数完整性
)

# 网络配置
PORTS="-p 7101:7101"
PRIVILEGED="--privileged"
SHM_SIZE="--shm-size=512m"

# 执行部署
docker run -d \
  --name "$CONTAINER" \
  $RESTART \
  "${VOLUMES[@]}" \
  "${ENV[@]}" \
  $PORTS \
  $PRIVILEGED \
  $SHM_SIZE \
  "$REG/$REPO:$TAG"

echo "[$CONTAINER] 判题服务已启动，端口 7101，共享内存 512MB"